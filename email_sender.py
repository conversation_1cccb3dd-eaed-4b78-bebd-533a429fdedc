#!/usr/bin/env python3
# email_sender.py - 邮件发送模块

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import os
import logging
import time
from typing import List, Dict, Optional
import json

class EmailSender:
    """邮件发送类"""
    
    def __init__(self, smtp_config: Dict):
        """
        初始化邮件发送器
        
        Args:
            smtp_config: SMTP配置字典，包含以下键：
                - smtp_server: smtp.feishu.com
                - smtp_port: 587
                - username: <EMAIL>
                - password: 1DL1keDggSi1RSQr 
                - use_tls: True
                - sender_name: Feng<PERSON>uan 
        """
        self.smtp_server = smtp_config.get('smtp_server')
        self.smtp_port = smtp_config.get('smtp_port', 587)
        self.username = smtp_config.get('username')
        self.password = smtp_config.get('password')
        self.use_tls = smtp_config.get('use_tls', True)
        self.use_ssl = smtp_config.get('use_ssl', False)
        self.sender_name = smtp_config.get('sender_name', '安全扫描系统')
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
    def create_html_report(self, host: str, results: Dict, warnings: List, 
                          critical_warnings: List, scan_time: str) -> str:
        """
        创建HTML格式的扫描报告
        
        Args:
            host: 扫描的主机地址
            results: 端口扫描结果
            warnings: 安全警告列表
            critical_warnings: 高风险警告列表
            scan_time: 扫描时间
            
        Returns:
            HTML格式的报告字符串
        """
        open_ports = [port for port, status in results.items() if status]
        closed_ports = [port for port, status in results.items() if not status]
        
        # 风险等级颜色
        risk_colors = {
            'critical': '#dc3545',  # 红色
            'medium': '#fd7e14',    # 橙色
            'low': '#ffc107',       # 黄色
            'safe': '#28a745'       # 绿色
        }
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>端口扫描安全审计报告</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .header h1 {{
                    color: #007bff;
                    margin: 0;
                    font-size: 28px;
                }}
                .info-box {{
                    background-color: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                    border-left: 4px solid #007bff;
                }}
                .warning-box {{
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
                .critical-box {{
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
                .safe-box {{
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
                .port-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                }}
                .port-table th, .port-table td {{
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: left;
                }}
                .port-table th {{
                    background-color: #007bff;
                    color: white;
                }}
                .port-open {{
                    color: #dc3545;
                    font-weight: bold;
                }}
                .port-closed {{
                    color: #6c757d;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    color: #6c757d;
                    font-size: 14px;
                }}
                .stats {{
                    display: flex;
                    justify-content: space-around;
                    margin: 20px 0;
                }}
                .stat-item {{
                    text-align: center;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 5px;
                    flex: 1;
                    margin: 0 5px;
                }}
                .stat-number {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #007bff;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔒 端口扫描安全审计报告</h1>
                    <p>自动化安全扫描系统</p>
                </div>
                
                <div class="info-box">
                    <h3>📋 扫描信息</h3>
                    <p><strong>目标主机:</strong> {host}</p>
                    <p><strong>扫描时间:</strong> {scan_time}</p>
                    <p><strong>扫描端口数:</strong> {len(results)} 个</p>
                </div>
                
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number">{len(open_ports)}</div>
                        <div>开放端口</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{len(closed_ports)}</div>
                        <div>关闭端口</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{len(warnings)}</div>
                        <div>安全警告</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{len(critical_warnings)}</div>
                        <div>高风险警告</div>
                    </div>
                </div>
        """
        
        # 添加安全警告部分
        if critical_warnings:
            html_template += f"""
                <div class="critical-box">
                    <h3>🚨 高风险警告 ({len(critical_warnings)} 个)</h3>
                    <ul>
            """
            for warning in critical_warnings:
                html_template += f"<li>{warning}</li>"
            html_template += "</ul></div>"
        
        if warnings and len(warnings) > len(critical_warnings):
            other_warnings = [w for w in warnings if w not in critical_warnings]
            html_template += f"""
                <div class="warning-box">
                    <h3>⚠️ 其他安全警告 ({len(other_warnings)} 个)</h3>
                    <ul>
            """
            for warning in other_warnings:
                html_template += f"<li>{warning}</li>"
            html_template += "</ul></div>"
        
        if not warnings:
            html_template += """
                <div class="safe-box">
                    <h3>✅ 安全状态良好</h3>
                    <p>未发现已知危险端口开放</p>
                </div>
            """
        
        # 添加端口详情表格
        html_template += """
                <h3>📊 端口扫描详情</h3>
                <table class="port-table">
                    <thead>
                        <tr>
                            <th>端口</th>
                            <th>状态</th>
                            <th>风险等级</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # 危险端口定义（简化版）
        danger_ports = {
            21: "高风险", 23: "高风险", 135: "高风险", 139: "高风险", 
            445: "高风险", 3389: "高风险", 5900: "高风险", 27017: "高风险",
            53: "中风险", 1433: "中风险", 3306: "中风险", 8080: "中风险"
        }
        
        for port in sorted(results.keys()):
            status = results[port]
            status_text = "开放" if status else "关闭"
            status_class = "port-open" if status else "port-closed"
            
            risk_level = "安全"
            if status and port in danger_ports:
                risk_level = danger_ports[port]
            
            html_template += f"""
                        <tr>
                            <td>{port}</td>
                            <td class="{status_class}">{status_text}</td>
                            <td>{risk_level}</td>
                        </tr>
            """
        
        html_template += """
                    </tbody>
                </table>
                
                <div class="footer">
                    <p>此报告由自动化安全扫描系统生成</p>
                    <p>如有疑问，请联系系统管理员</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def send_email(self, recipients: List[str], subject: str, 
                   html_content: str, text_content: str = None,
                   attachments: List[str] = None, max_retries: int = 3) -> bool:
        """
        发送邮件
        
        Args:
            recipients: <EMAIL>
            subject: 237端口扫描结果报告
            html_content: HTML内容
            text_content: 纯文本内容（可选）
            attachments: 附件文件路径列表（可选）
            max_retries: 最大重试次数
            
        Returns:
            发送是否成功
        """
        for attempt in range(max_retries):
            try:
                # 创建邮件对象
                msg = MIMEMultipart('alternative')
                msg['From'] = f"{self.sender_name} <{self.username}>"
                msg['To'] = ', '.join(recipients)
                msg['Subject'] = subject
                
                # 添加文本内容
                if text_content:
                    text_part = MIMEText(text_content, 'plain', 'utf-8')
                    msg.attach(text_part)
                
                # 添加HTML内容
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(html_part)
                
                # 添加附件
                if attachments:
                    for file_path in attachments:
                        if os.path.exists(file_path):
                            with open(file_path, 'rb') as attachment:
                                part = MIMEBase('application', 'octet-stream')
                                part.set_payload(attachment.read())
                                encoders.encode_base64(part)
                                part.add_header(
                                    'Content-Disposition',
                                    f'attachment; filename= {os.path.basename(file_path)}'
                                )
                                msg.attach(part)
                
                # 发送邮件
                context = ssl.create_default_context()

                if self.use_ssl:
                    # 使用SSL连接（端口465）
                    with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port, context=context) as server:
                        server.login(self.username, self.password)
                        server.send_message(msg)
                else:
                    # 使用TLS连接（端口587）
                    with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                        if self.use_tls:
                            server.starttls(context=context)
                        server.login(self.username, self.password)
                        server.send_message(msg)
                
                self.logger.info(f"邮件发送成功，收件人: {', '.join(recipients)}")
                return True
                
            except Exception as e:
                self.logger.error(f"邮件发送失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    self.logger.error(f"邮件发送最终失败，已重试 {max_retries} 次")
                    return False
        
        return False

def load_email_config(config_file: str = 'email_config.json') -> Dict:
    """
    从配置文件加载邮件配置
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        # 返回默认配置模板
        default_config = {
            "smtp_config": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "<EMAIL>",
                "password": "your_app_password",
                "use_tls": True,
                "sender_name": "安全扫描系统"
            },
            "recipients": [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "email_settings": {
                "subject_template": "🔒 安全扫描报告 - {host} - {date}",
                "include_attachments": True,
                "max_retries": 3
            }
        }
        
        # 保存默认配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        print(f"已创建默认配置文件: {config_file}")
        print("请编辑配置文件中的邮箱设置后重新运行程序")
        
        return default_config

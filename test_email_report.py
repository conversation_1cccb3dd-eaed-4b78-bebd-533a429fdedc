#!/usr/bin/env python3
# test_email_report.py - 测试邮件发送和报告生成

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from email_sender import EmailSender, load_email_config
from report_generator import ReportGenerator
from config_manager import ConfigManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_email.log', encoding='utf-8')
        ]
    )

def create_test_scan_results():
    """创建测试扫描结果"""
    # 模拟扫描结果，包含一些危险端口
    test_results = {
        21: True,    # FTP - 高风险
        22: True,    # SSH - 安全
        23: False,   # Telnet - 高风险但关闭
        53: True,    # DNS - 中风险
        80: True,    # HTTP - 安全
        135: True,   # RPC - 高风险
        139: False,  # NetBIOS - 高风险但关闭
        443: True,   # HTTPS - 安全
        445: True,   # SMB - 高风险
        1433: False, # MSSQL - 中风险但关闭
        3306: True,  # MySQL - 中风险
        3389: False, # RDP - 高风险但关闭
        5900: True,  # VNC - 高风险
        6379: True,  # Redis - 高风险
        8080: True,  # HTTP-Alt - 中风险
        27017: True, # MongoDB - 高风险
    }
    return test_results

def test_report_generation():
    """测试报告生成功能"""
    print("🧪 测试报告生成功能...")
    
    # 创建报告生成器
    report_gen = ReportGenerator()
    
    # 创建测试数据
    host = "***************"
    results = create_test_scan_results()
    
    # 进行安全检查
    warnings, critical_warnings = report_gen.security_check(results)
    
    print(f"扫描结果: {len(results)} 个端口")
    print(f"开放端口: {sum(results.values())} 个")
    print(f"安全警告: {len(warnings)} 个")
    print(f"高风险警告: {len(critical_warnings)} 个")
    
    # 生成报告
    report_files = report_gen.save_report(host, results, warnings, critical_warnings)
    
    print(f"✅ 文本报告已生成: {report_files['text']}")
    print(f"✅ HTML报告已生成: {report_files['html']}")
    
    return report_files

def test_email_sending(report_files, test_mode=True):
    """测试邮件发送功能"""
    print("\n📧 测试邮件发送功能...")
    
    try:
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 检查配置
        errors = config_manager.validate_config(config)
        if errors:
            print("❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            print("\n请编辑 scan_config.json 文件，配置正确的邮箱设置")
            return False
        
        # 创建邮件发送器
        smtp_config = {
            'smtp_server': config.smtp.smtp_server,
            'smtp_port': config.smtp.smtp_port,
            'username': config.smtp.username,
            'password': config.smtp.password,
            'use_tls': config.smtp.use_tls,
            'sender_name': config.smtp.sender_name
        }
        
        email_sender = EmailSender(smtp_config)
        
        # 准备邮件内容
        host = "***************"
        scan_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        subject = config.email.subject_template.format(
            host=host,
            date=datetime.now().strftime("%Y-%m-%d")
        )
        
        # 使用生成的HTML内容
        html_content = report_files['html_content']
        text_content = f"安全扫描报告已生成，请查看附件。\\n\\n扫描时间: {scan_time}\\n目标主机: {host}"
        
        # 准备附件
        attachments = []
        if config.email.include_attachments:
            attachments = [report_files['text'], report_files['html']]
        
        if test_mode:
            print("🔍 测试模式 - 邮件内容预览:")
            print(f"收件人: {', '.join(config.email.recipients)}")
            print(f"主题: {subject}")
            print(f"附件: {len(attachments)} 个文件")
            print("\\n如需实际发送邮件，请将 test_mode 设置为 False")
            return True
        else:
            # 实际发送邮件
            success = email_sender.send_email(
                recipients=config.email.recipients,
                subject=subject,
                html_content=html_content,
                text_content=text_content,
                attachments=attachments,
                max_retries=config.email.max_retries
            )
            
            if success:
                print("✅ 邮件发送成功!")
                return True
            else:
                print("❌ 邮件发送失败!")
                return False
                
    except Exception as e:
        print(f"❌ 邮件发送测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试邮件报告系统...")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    try:
        # 测试报告生成
        report_files = test_report_generation()
        
        # 测试邮件发送（实际发送）
        email_success = test_email_sending(report_files, test_mode=False)
        
        print("\\n" + "=" * 50)
        if email_success:
            print("✅ 所有测试通过!")
            print("\\n📝 下一步操作:")
            print("1. 编辑 scan_config.json 文件，配置您的邮箱设置")
            print("2. 在 email.recipients 中添加收件人邮箱")
            print("3. 运行实际的安全扫描和邮件发送")
            print("\\n💡 提示:")
            print("- 如使用Gmail，需要启用'应用专用密码'")
            print("- 确保SMTP服务器和端口设置正确")
            print("- 检查防火墙是否允许SMTP连接")
        else:
            print("❌ 测试失败，请检查配置和网络连接")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        logging.error(f"测试失败: {e}", exc_info=True)

if __name__ == "__main__":
    main()

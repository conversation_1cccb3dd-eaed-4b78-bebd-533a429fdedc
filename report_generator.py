#!/usr/bin/env python3
# report_generator.py - 增强的报告生成模块

import datetime
import os
import json
from typing import Dict, List, Tuple, Optional
import logging

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 危险端口定义（完整版）
        self.danger_ports = {
            # 远程访问和管理
            21: "FTP - 文件传输协议(明文传输)",
            23: "Telnet - 远程登录(明文传输)",
            512: "rexec - 远程执行(明文传输)",
            513: "rlogin - 远程登录(明文传输)",
            514: "rsh - 远程shell(明文传输)",
            
            # Windows相关
            135: "RPC - 远程过程调用",
            137: "NetBIOS-NS - NetBIOS名称服务",
            138: "NetBIOS-DGM - NetBIOS数据报服务",
            139: "NetBIOS-SSN - NetBIOS会话服务",
            445: "SMB/CIFS - 服务器消息块协议",
            1433: "MSSQL - Microsoft SQL Server",
            1434: "MSSQL Monitor - SQL Server监控",
            1521: "Oracle - Oracle数据库",
            3389: "RDP - 远程桌面协议",
            
            # 数据库服务
            3306: "MySQL - MySQL数据库",
            5432: "PostgreSQL - PostgreSQL数据库",
            27017: "MongoDB - MongoDB数据库(无认证风险)",
            27018: "MongoDB - MongoDB分片端口",
            27019: "MongoDB - MongoDB配置端口",
            6379: "Redis - Redis数据库(无认证风险)",
            11211: "Memcached - 内存缓存系统",
            9200: "Elasticsearch - 搜索引擎",
            9300: "Elasticsearch - 集群通信",
            
            # 远程管理
            2222: "DirectAdmin - 控制面板",
            2082: "cPanel - 控制面板",
            2083: "cPanel SSL - 控制面板SSL",
            2086: "WebHost Manager - 主机管理",
            2087: "WebHost Manager SSL - 主机管理SSL",
            2095: "Webmail - 网络邮件",
            2096: "Webmail SSL - 网络邮件SSL",
            
            # 其他危险服务
            53: "DNS - 域名系统(可能被用于DDoS)",
            5900: "VNC - 远程桌面(弱认证风险)",
            5901: "VNC - 远程桌面备用端口",
            6000: "X11 - X Window系统(图形界面)",
            6667: "IRC - 互联网中继聊天",
            6697: "IRC SSL - 加密IRC",
            873: "rsync - 文件同步服务",
            9999: "常见后门端口",
            3128: "Squid代理 - HTTP代理",
            8080: "HTTP代理/备用Web服务",
            8000: "HTTP备用端口",
            8888: "HTTP备用端口/代理",
            1080: "SOCKS代理",
            
            # 工业控制系统
            44818: "EtherNet/IP - 工业协议",
            47808: "BACnet - 建筑自动化",
            102: "Siemens S7 - 西门子PLC",
            502: "Modbus - 工业通信协议",
            
            # 其他
            161: "SNMP - 简单网络管理协议",
            162: "SNMP Trap - SNMP陷阱",
            1900: "SSDP - 简单服务发现协议",
            5353: "mDNS - 多播DNS",
            111: "Portmapper - 端口映射",
            1194: "OpenVPN - 虚拟私人网络",
            1723: "PPTP - 点对点隧道协议",
            5060: "SIP - 会话初始协议",
            5061: "SIP TLS - 加密SIP"
        }
        
        # 风险等级分类
        self.critical_ports = [21, 23, 135, 139, 445, 3389, 5900, 27017, 6379, 11211, 9200]
        self.medium_ports = [53, 1433, 1521, 3306, 5432, 2082, 2083, 2222, 8080, 8000, 8888]
    
    def security_check(self, results: Dict[int, bool]) -> Tuple[List[str], List[str]]:
        """
        安全检查 - 识别危险端口
        
        Args:
            results: 端口扫描结果字典
            
        Returns:
            (所有警告列表, 高风险警告列表)
        """
        warnings = []
        critical_warnings = []
        medium_warnings = []
        low_warnings = []
        
        for port, is_open in results.items():
            if is_open and port in self.danger_ports:
                warning_msg = f"端口 {port} ({self.danger_ports[port]}) 开放"
                
                if port in self.critical_ports:
                    critical_warnings.append(warning_msg)
                elif port in self.medium_ports:
                    medium_warnings.append(warning_msg)
                else:
                    low_warnings.append(warning_msg)
        
        # 按严重程度排序
        warnings = critical_warnings + medium_warnings + low_warnings
        return warnings, critical_warnings
    
    def generate_text_report(self, host: str, results: Dict[int, bool], 
                           warnings: List[str], critical_warnings: List[str],
                           scan_time: str = None) -> str:
        """
        生成文本格式报告
        
        Args:
            host: 扫描的主机地址
            results: 端口扫描结果
            warnings: 安全警告列表
            critical_warnings: 高风险警告列表
            scan_time: 扫描时间
            
        Returns:
            文本格式的报告
        """
        if not scan_time:
            scan_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report_lines = []
        report_lines.append("端口扫描安全审计报告")
        report_lines.append("=" * 60)
        report_lines.append(f"扫描时间: {scan_time}")
        report_lines.append(f"目标主机: {host}")
        report_lines.append(f"扫描端口数: {len(results)} 个")
        report_lines.append("")
        
        # 统计信息
        open_ports = [port for port, status in results.items() if status]
        closed_ports = [port for port, status in results.items() if not status]
        
        report_lines.append("扫描统计:")
        report_lines.append("-" * 30)
        report_lines.append(f"开放端口: {len(open_ports)} 个")
        report_lines.append(f"关闭端口: {len(closed_ports)} 个")
        report_lines.append(f"安全警告: {len(warnings)} 个")
        report_lines.append(f"高风险警告: {len(critical_warnings)} 个")
        report_lines.append("")
        
        # 安全警告
        if warnings:
            report_lines.append("安全警告:")
            report_lines.append("-" * 30)
            for warning in warnings:
                report_lines.append(f"  ⚠️  {warning}")
            
            if critical_warnings:
                report_lines.append("")
                report_lines.append(f"🚨 高风险端口 ({len(critical_warnings)} 个):")
                for warning in critical_warnings:
                    report_lines.append(f"  🚨 {warning}")
        else:
            report_lines.append("✅ 未发现已知危险端口开放")
        
        report_lines.append("")
        report_lines.append("端口状态详情:")
        report_lines.append("-" * 30)
        for port in sorted(results.keys()):
            status = results[port]
            status_text = "开放" if status else "关闭"
            risk_info = ""
            if status and port in self.danger_ports:
                if port in self.critical_ports:
                    risk_info = " [高风险]"
                elif port in self.medium_ports:
                    risk_info = " [中风险]"
                else:
                    risk_info = " [低风险]"
            
            report_lines.append(f"  端口 {port:5d}: {status_text}{risk_info}")
        
        return "\n".join(report_lines)
    
    def generate_html_report(self, host: str, results: Dict[int, bool], 
                           warnings: List[str], critical_warnings: List[str],
                           scan_time: str = None) -> str:
        """
        生成HTML格式报告
        
        Args:
            host: 扫描的主机地址
            results: 端口扫描结果
            warnings: 安全警告列表
            critical_warnings: 高风险警告列表
            scan_time: 扫描时间
            
        Returns:
            HTML格式的报告
        """
        if not scan_time:
            scan_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        open_ports = [port for port, status in results.items() if status]
        closed_ports = [port for port, status in results.items() if not status]
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>端口扫描安全审计报告</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 900px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .header h1 {{
                    color: #007bff;
                    margin: 0;
                    font-size: 28px;
                }}
                .info-box {{
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 25px;
                    border-left: 4px solid #007bff;
                }}
                .stats {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 15px;
                    margin: 25px 0;
                }}
                .stat-item {{
                    text-align: center;
                    padding: 20px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;
                }}
                .stat-number {{
                    font-size: 32px;
                    font-weight: bold;
                    color: #007bff;
                    display: block;
                }}
                .stat-label {{
                    color: #6c757d;
                    font-size: 14px;
                    margin-top: 5px;
                }}
                .warning-box {{
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 25px;
                }}
                .critical-box {{
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 25px;
                }}
                .safe-box {{
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 25px;
                }}
                .port-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    font-size: 14px;
                }}
                .port-table th, .port-table td {{
                    border: 1px solid #ddd;
                    padding: 12px 8px;
                    text-align: left;
                }}
                .port-table th {{
                    background-color: #007bff;
                    color: white;
                    font-weight: bold;
                }}
                .port-table tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                .port-open {{
                    color: #dc3545;
                    font-weight: bold;
                }}
                .port-closed {{
                    color: #6c757d;
                }}
                .risk-critical {{
                    background-color: #f8d7da;
                    color: #721c24;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                }}
                .risk-medium {{
                    background-color: #fff3cd;
                    color: #856404;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                }}
                .risk-low {{
                    background-color: #d1ecf1;
                    color: #0c5460;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                }}
                .risk-safe {{
                    background-color: #d4edda;
                    color: #155724;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    color: #6c757d;
                    font-size: 14px;
                }}
                ul {{
                    margin: 10px 0;
                    padding-left: 20px;
                }}
                li {{
                    margin: 8px 0;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔒 端口扫描安全审计报告</h1>
                    <p>自动化安全扫描系统</p>
                </div>
                
                <div class="info-box">
                    <h3>📋 扫描信息</h3>
                    <p><strong>目标主机:</strong> {host}</p>
                    <p><strong>扫描时间:</strong> {scan_time}</p>
                    <p><strong>扫描端口数:</strong> {len(results)} 个</p>
                </div>
                
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-number">{len(open_ports)}</span>
                        <div class="stat-label">开放端口</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{len(closed_ports)}</span>
                        <div class="stat-label">关闭端口</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{len(warnings)}</span>
                        <div class="stat-label">安全警告</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{len(critical_warnings)}</span>
                        <div class="stat-label">高风险警告</div>
                    </div>
                </div>
        """
        
        # 添加安全警告部分
        if critical_warnings:
            html_template += f"""
                <div class="critical-box">
                    <h3>🚨 高风险警告 ({len(critical_warnings)} 个)</h3>
                    <ul>
            """
            for warning in critical_warnings:
                html_template += f"<li><strong>{warning}</strong></li>"
            html_template += "</ul></div>"
        
        if warnings and len(warnings) > len(critical_warnings):
            other_warnings = [w for w in warnings if w not in critical_warnings]
            html_template += f"""
                <div class="warning-box">
                    <h3>⚠️ 其他安全警告 ({len(other_warnings)} 个)</h3>
                    <ul>
            """
            for warning in other_warnings:
                html_template += f"<li>{warning}</li>"
            html_template += "</ul></div>"
        
        if not warnings:
            html_template += """
                <div class="safe-box">
                    <h3>✅ 安全状态良好</h3>
                    <p>未发现已知危险端口开放</p>
                </div>
            """
        
        # 添加端口详情表格
        html_template += """
                <h3>📊 端口扫描详情</h3>
                <table class="port-table">
                    <thead>
                        <tr>
                            <th>端口</th>
                            <th>状态</th>
                            <th>风险等级</th>
                            <th>服务描述</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        for port in sorted(results.keys()):
            status = results[port]
            status_text = "开放" if status else "关闭"
            status_class = "port-open" if status else "port-closed"
            
            risk_level = "安全"
            risk_class = "risk-safe"
            service_desc = "未知服务"
            
            if status and port in self.danger_ports:
                service_desc = self.danger_ports[port]
                if port in self.critical_ports:
                    risk_level = "高风险"
                    risk_class = "risk-critical"
                elif port in self.medium_ports:
                    risk_level = "中风险"
                    risk_class = "risk-medium"
                else:
                    risk_level = "低风险"
                    risk_class = "risk-low"
            
            html_template += f"""
                        <tr>
                            <td><strong>{port}</strong></td>
                            <td class="{status_class}">{status_text}</td>
                            <td><span class="{risk_class}">{risk_level}</span></td>
                            <td>{service_desc}</td>
                        </tr>
            """
        
        html_template += """
                    </tbody>
                </table>
                
                <div class="footer">
                    <p>此报告由自动化安全扫描系统生成</p>
                    <p>如有疑问，请联系系统管理员</p>
                    <p>生成时间: """ + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def save_report(self, host: str, results: Dict[int, bool], 
                   warnings: List[str], critical_warnings: List[str],
                   output_dir: str = "reports", filename_prefix: str = None) -> Dict[str, str]:
        """
        保存报告文件（文本和HTML格式）
        
        Args:
            host: 扫描的主机地址
            results: 端口扫描结果
            warnings: 安全警告列表
            critical_warnings: 高风险警告列表
            output_dir: 输出目录
            filename_prefix: 文件名前缀
            
        Returns:
            保存的文件路径字典 {'text': 'path', 'html': 'path'}
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        if not filename_prefix:
            filename_prefix = f"scan_report_{host.replace('.', '_')}"
        
        scan_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 生成文本报告
        text_content = self.generate_text_report(host, results, warnings, critical_warnings, scan_time)
        text_filename = os.path.join(output_dir, f"{filename_prefix}_{timestamp}.txt")
        
        with open(text_filename, 'w', encoding='utf-8') as f:
            f.write(text_content)
        
        # 生成HTML报告
        html_content = self.generate_html_report(host, results, warnings, critical_warnings, scan_time)
        html_filename = os.path.join(output_dir, f"{filename_prefix}_{timestamp}.html")
        
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"报告已保存: {text_filename}, {html_filename}")
        
        return {
            'text': text_filename,
            'html': html_filename,
            'scan_time': scan_time,
            'html_content': html_content,
            'text_content': text_content
        }

# 安全扫描定时任务系统

一个功能完整的自动化安全扫描系统，支持定期执行端口扫描并将结果通过邮件推送到多个指定邮箱。

## 🌟 主要功能

- **自动化端口扫描**: 支持多主机、多端口的安全扫描
- **智能安全分析**: 自动识别危险端口并按风险等级分类
- **定时任务调度**: 支持多种调度模式（每日、间隔、每周）
- **邮件报告推送**: 生成HTML格式报告并自动发送到多个邮箱
- **完整日志记录**: 详细的扫描、邮件发送和错误日志
- **灵活配置管理**: JSON格式配置文件，支持热更新

## 📁 项目结构

```
scan_securit/
├── security_scan.py           # 原始扫描脚本
├── security_scan_scheduler.py # 主控制脚本
├── start_scan_service.py      # 启动检查脚本
├── config_manager.py          # 配置管理模块
├── email_sender.py            # 邮件发送模块
├── report_generator.py        # 报告生成模块
├── scheduler.py               # 定时任务调度器
├── logger_config.py           # 日志配置模块
├── test_email_report.py       # 邮件测试脚本
├── scan_config.json           # 配置文件
├── README.md                  # 使用说明
├── logs/                      # 日志目录
└── reports/                   # 报告输出目录
```

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装 Python 3.6+：

```bash
python3 --version
```

### 2. 安装依赖

```bash
pip3 install schedule
```

### 3. 初始化配置

运行启动检查脚本：

```bash
python3 start_scan_service.py
```

这个脚本会：
- 检查依赖包
- 创建默认配置文件
- 提供配置指导

### 4. 编辑配置文件

编辑 `scan_config.json` 文件，修改以下关键配置：

#### SMTP邮箱设置
```json
{
  "smtp": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your_app_password",
    "use_tls": true,
    "sender_name": "安全扫描系统"
  }
}
```

#### 收件人设置
```json
{
  "email": {
    "recipients": [
      "<EMAIL>",
      "<EMAIL>"
    ]
  }
}
```

#### 扫描目标
```json
{
  "scan": {
    "target_hosts": [
      "*************",
      "*********"
    ]
  }
}
```

### 5. 测试系统

#### 测试邮件发送
```bash
python3 test_email_report.py
```

#### 执行单次扫描
```bash
python3 security_scan_scheduler.py -m once
```

#### 启动定时服务
```bash
python3 security_scan_scheduler.py -m daemon
```

## 📧 邮箱配置指南

### Gmail 配置

1. 启用两步验证
2. 生成应用专用密码：
   - 访问 https://myaccount.google.com/apppasswords
   - 选择"邮件"和您的设备
   - 复制生成的16位密码

3. 配置示例：
```json
{
  "smtp": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "abcd efgh ijkl mnop",
    "use_tls": true
  }
}
```

### 其他邮箱服务商

| 服务商   | SMTP服务器            | 端口 | 加密 |
| -------- | --------------------- | ---- | ---- |
| QQ邮箱   | smtp.qq.com           | 587  | TLS  |
| 163邮箱  | smtp.163.com          | 587  | TLS  |
| Outlook  | smtp-mail.outlook.com | 587  | TLS  |
| 企业邮箱 | 咨询您的IT管理员      | -    | -    |

## ⏰ 定时任务配置

### 每日定时执行
```json
{
  "schedule": {
    "enabled": true,
    "schedule_type": "daily",
    "cron_expression": "0 2 * * *"
  }
}
```

### 间隔执行
```json
{
  "schedule": {
    "enabled": true,
    "schedule_type": "interval",
    "interval_hours": 6
  }
}
```

### 每周执行
```json
{
  "schedule": {
    "enabled": true,
    "schedule_type": "weekly",
    "cron_expression": "0 2 * * 1"
  }
}
```

## 🔧 命令行使用

### 基本命令

```bash
# 查看帮助
python3 security_scan_scheduler.py -h

# 单次扫描所有配置的主机
python3 security_scan_scheduler.py -m once

# 扫描指定主机
python3 security_scan_scheduler.py -m once -H *************

# 启动定时服务（前台运行）
python3 security_scan_scheduler.py -m daemon

# 查看服务状态
python3 security_scan_scheduler.py -m status

# 使用自定义配置文件
python3 security_scan_scheduler.py -c my_config.json -m once
```

### 后台运行

```bash
# Linux/macOS 后台运行
nohup python3 security_scan_scheduler.py -m daemon > scan_service.log 2>&1 &

# 查看进程
ps aux | grep security_scan_scheduler

# 停止服务
kill $(cat scheduler.pid)
```

## 📊 报告格式

系统会生成两种格式的报告：

### HTML报告
- 美观的网页格式
- 包含风险等级颜色标识
- 统计图表和详细表格
- 适合邮件查看

### 文本报告
- 纯文本格式
- 便于日志记录
- 适合自动化处理

## 📝 日志管理

日志文件位置：
- `logs/security_scan.log` - 主日志
- `logs/scan.log` - 扫描专用日志
- `logs/email.log` - 邮件发送日志
- `logs/scheduler.log` - 调度器日志
- `logs/*_error.log` - 各模块错误日志

## 🔒 安全考虑

1. **配置文件安全**：
   - 保护配置文件权限 `chmod 600 scan_config.json`
   - 不要将包含密码的配置文件提交到版本控制

2. **网络安全**：
   - 确保扫描行为符合网络安全政策
   - 仅扫描您有权限的主机

3. **邮件安全**：
   - 使用应用专用密码而非账户密码
   - 启用邮箱的两步验证

## 🐛 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查SMTP配置是否正确
   - 确认网络连接正常
   - 验证邮箱密码和权限

2. **扫描超时**
   - 增加timeout配置值
   - 检查目标主机网络连通性

3. **权限错误**
   - 确保有写入日志和报告目录的权限
   - 检查PID文件的写入权限

### 调试模式

```bash
# 启用详细日志
# 编辑配置文件，设置 "log_level": "DEBUG"

# 查看实时日志
tail -f logs/security_scan.log
```

## 📞 技术支持

如果您遇到问题：

1. 查看日志文件获取详细错误信息
2. 确认配置文件格式正确
3. 验证网络连接和权限设置
4. 参考本文档的故障排除部分

## 📄 许可证

本项目仅供学习和内部使用，请遵守相关法律法规和网络安全政策。


#!/usr/bin/env python3
# start_scan_service.py - 安全扫描服务启动脚本

import os
import sys
import json
from datetime import datetime

def check_dependencies():
    """检查依赖包"""
    required_packages = ['schedule']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少必要的依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip3 install {' '.join(missing_packages)}")
        return False
    
    return True

def check_config_file():
    """检查配置文件"""
    config_file = "scan_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        print("正在创建默认配置文件...")
        
        # 创建配置文件
        os.system("python3 config_manager.py")
        
        if os.path.exists(config_file):
            print(f"✅ 已创建配置文件: {config_file}")
            return False  # 需要用户编辑配置
        else:
            print("❌ 创建配置文件失败")
            return False
    
    # 检查配置内容
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置项
        smtp_config = config.get('smtp', {})
        if (smtp_config.get('username') == '<EMAIL>' or 
            smtp_config.get('password') == 'your_app_password'):
            print("⚠️ 检测到默认邮箱配置，请编辑配置文件")
            return False
        
        email_config = config.get('email', {})
        recipients = email_config.get('recipients', [])
        if not recipients or '<EMAIL>' in recipients:
            print("⚠️ 检测到默认收件人配置，请编辑配置文件")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件格式错误: {e}")
        return False

def show_config_help():
    """显示配置帮助"""
    print("\n📝 配置文件说明:")
    print("请编辑 scan_config.json 文件，修改以下配置项：")
    print()
    print("1. SMTP邮箱设置 (smtp 部分):")
    print("   - smtp_server: SMTP服务器地址")
    print("   - smtp_port: SMTP端口 (通常为587)")
    print("   - username: 发件人邮箱地址")
    print("   - password: 邮箱密码或应用专用密码")
    print()
    print("2. 收件人设置 (email.recipients):")
    print("   - 添加实际的收件人邮箱地址")
    print()
    print("3. 扫描目标 (scan.target_hosts):")
    print("   - 修改为您要扫描的服务器IP地址")
    print()
    print("4. 定时设置 (schedule 部分):")
    print("   - schedule_type: 调度类型 (daily/interval/weekly)")
    print("   - interval_hours: 间隔小时数 (仅interval模式)")
    print("   - cron_expression: cron表达式 (仅daily模式)")
    print()
    print("💡 Gmail用户提示:")
    print("- 需要启用'两步验证'")
    print("- 使用'应用专用密码'而不是账户密码")
    print("- 应用专用密码生成地址: https://myaccount.google.com/apppasswords")

def show_usage():
    """显示使用说明"""
    print("\n🚀 安全扫描服务使用说明:")
    print()
    print("1. 单次执行扫描:")
    print("   python3 security_scan_scheduler.py -m once")
    print()
    print("2. 扫描指定主机:")
    print("   python3 security_scan_scheduler.py -m once -H ***********")
    print()
    print("3. 启动定时服务:")
    print("   python3 security_scan_scheduler.py -m daemon")
    print()
    print("4. 查看服务状态:")
    print("   python3 security_scan_scheduler.py -m status")
    print()
    print("5. 测试邮件发送:")
    print("   python3 test_email_report.py")

def main():
    """主函数"""
    print("🔒 安全扫描服务启动检查")
    print("=" * 50)
    
    # 检查依赖
    print("📦 检查依赖包...")
    if not check_dependencies():
        return
    print("✅ 依赖包检查通过")
    
    # 检查配置文件
    print("\n⚙️ 检查配置文件...")
    if not check_config_file():
        show_config_help()
        print("\n请编辑配置文件后重新运行此脚本")
        return
    print("✅ 配置文件检查通过")
    
    # 显示使用说明
    show_usage()
    
    print("\n" + "=" * 50)
    print("✅ 所有检查通过！您现在可以启动安全扫描服务了")
    print()
    
    # 询问用户是否要立即启动
    try:
        choice = input("是否要立即执行一次扫描测试？(y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            print("\n🔍 执行测试扫描...")
            os.system("python3 security_scan_scheduler.py -m once")
        else:
            print("您可以稍后手动启动服务")
    except KeyboardInterrupt:
        print("\n\n👋 再见！")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# logger_config.py - 日志配置和管理模块

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Optional
import json

class LoggerManager:
    """日志管理器"""
    
    def __init__(self, config=None):
        self.config = config
        self.loggers = {}
        
    def setup_logger(self, name: str = "security_scan", 
                    log_file: str = None, 
                    log_level: str = "INFO",
                    max_size_mb: int = 10,
                    backup_count: int = 5,
                    console_output: bool = True,
                    format_string: str = None) -> logging.Logger:
        """
        设置日志记录器
        
        Args:
            name: 日志记录器名称
            log_file: 日志文件路径
            log_level: 日志级别
            max_size_mb: 日志文件最大大小(MB)
            backup_count: 备份文件数量
            console_output: 是否输出到控制台
            format_string: 自定义格式字符串
            
        Returns:
            配置好的日志记录器
        """
        # 如果已存在，直接返回
        if name in self.loggers:
            return self.loggers[name]
        
        # 创建日志记录器
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 设置日志格式
        if not format_string:
            format_string = (
                '%(asctime)s - %(name)s - %(levelname)s - '
                '[%(filename)s:%(lineno)d] - %(message)s'
            )
        
        formatter = logging.Formatter(
            format_string,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 文件处理器（带轮转）
        if log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_size_mb * 1024 * 1024,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        # 控制台处理器
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 错误日志单独处理器
        if log_file:
            error_log_file = log_file.replace('.log', '_error.log')
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=max_size_mb * 1024 * 1024,
                backupCount=backup_count,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            logger.addHandler(error_handler)
        
        self.loggers[name] = logger
        return logger
    
    def setup_from_config(self, config):
        """从配置对象设置日志"""
        return self.setup_logger(
            name="security_scan",
            log_file=config.logging.log_file,
            log_level=config.logging.log_level,
            max_size_mb=config.logging.max_log_size_mb,
            backup_count=config.logging.backup_count,
            console_output=config.logging.console_output
        )
    
    def get_logger(self, name: str = "security_scan") -> logging.Logger:
        """获取日志记录器"""
        return self.loggers.get(name, logging.getLogger(name))
    
    def create_scan_logger(self) -> logging.Logger:
        """创建扫描专用日志记录器"""
        return self.setup_logger(
            name="scan",
            log_file="logs/scan.log",
            log_level="INFO",
            console_output=False
        )
    
    def create_email_logger(self) -> logging.Logger:
        """创建邮件专用日志记录器"""
        return self.setup_logger(
            name="email",
            log_file="logs/email.log",
            log_level="INFO",
            console_output=False
        )
    
    def create_scheduler_logger(self) -> logging.Logger:
        """创建调度器专用日志记录器"""
        return self.setup_logger(
            name="scheduler",
            log_file="logs/scheduler.log",
            log_level="INFO",
            console_output=False
        )

class ScanLogger:
    """扫描日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.scan_start_time = None
        self.scan_results = {}
    
    def start_scan(self, host: str, ports: list):
        """开始扫描"""
        self.scan_start_time = datetime.now()
        self.logger.info(f"开始扫描主机: {host}, 端口数量: {len(ports)}")
        self.logger.info(f"扫描端口列表: {ports}")
    
    def log_port_result(self, host: str, port: int, is_open: bool, response_time: float = None):
        """记录端口扫描结果"""
        status = "开放" if is_open else "关闭"
        time_info = f", 响应时间: {response_time:.3f}s" if response_time else ""
        self.logger.debug(f"主机 {host} 端口 {port}: {status}{time_info}")
    
    def log_security_warning(self, warning: str, level: str = "WARNING"):
        """记录安全警告"""
        if level.upper() == "CRITICAL":
            self.logger.critical(f"🚨 高风险: {warning}")
        else:
            self.logger.warning(f"⚠️ 安全警告: {warning}")
    
    def end_scan(self, host: str, total_ports: int, open_ports: int, warnings: int):
        """结束扫描"""
        if self.scan_start_time:
            duration = datetime.now() - self.scan_start_time
            self.logger.info(
                f"扫描完成 - 主机: {host}, "
                f"总端口: {total_ports}, 开放: {open_ports}, "
                f"警告: {warnings}, 耗时: {duration.total_seconds():.2f}s"
            )
        else:
            self.logger.info(f"扫描完成 - 主机: {host}")

class EmailLogger:
    """邮件日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def log_email_start(self, recipients: list, subject: str):
        """记录邮件发送开始"""
        self.logger.info(f"开始发送邮件 - 收件人: {len(recipients)}个, 主题: {subject}")
        self.logger.debug(f"收件人列表: {recipients}")
    
    def log_email_success(self, recipients: list):
        """记录邮件发送成功"""
        self.logger.info(f"邮件发送成功 - 收件人: {recipients}")
    
    def log_email_failure(self, recipients: list, error: str, attempt: int = 1):
        """记录邮件发送失败"""
        self.logger.error(f"邮件发送失败 (尝试 {attempt}) - 收件人: {recipients}, 错误: {error}")
    
    def log_email_retry(self, attempt: int, max_attempts: int):
        """记录邮件重试"""
        self.logger.warning(f"邮件发送重试 {attempt}/{max_attempts}")

class SchedulerLogger:
    """调度器日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def log_scheduler_start(self, jobs_count: int):
        """记录调度器启动"""
        self.logger.info(f"调度器启动 - 任务数量: {jobs_count}")
    
    def log_scheduler_stop(self):
        """记录调度器停止"""
        self.logger.info("调度器已停止")
    
    def log_job_start(self, job_name: str):
        """记录任务开始"""
        self.logger.info(f"任务开始执行: {job_name}")
    
    def log_job_complete(self, job_name: str, duration: float):
        """记录任务完成"""
        self.logger.info(f"任务执行完成: {job_name}, 耗时: {duration:.2f}s")
    
    def log_job_error(self, job_name: str, error: str):
        """记录任务错误"""
        self.logger.error(f"任务执行失败: {job_name}, 错误: {error}")
    
    def log_next_run(self, job_name: str, next_run_time: datetime):
        """记录下次执行时间"""
        self.logger.info(f"任务 {job_name} 下次执行时间: {next_run_time}")

class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_file: str):
        self.log_file = log_file
    
    def get_recent_scans(self, hours: int = 24) -> list:
        """获取最近的扫描记录"""
        scans = []
        if not os.path.exists(self.log_file):
            return scans
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                if "扫描完成" in line:
                    # 解析扫描结果
                    # 这里可以添加更复杂的解析逻辑
                    scans.append(line.strip())
        
        except Exception as e:
            logging.error(f"分析日志文件失败: {e}")
        
        return scans
    
    def get_error_summary(self, hours: int = 24) -> dict:
        """获取错误摘要"""
        errors = {
            'scan_errors': 0,
            'email_errors': 0,
            'scheduler_errors': 0,
            'other_errors': 0
        }
        
        if not os.path.exists(self.log_file):
            return errors
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                if " - ERROR - " in line:
                    if "scan" in line.lower():
                        errors['scan_errors'] += 1
                    elif "email" in line.lower():
                        errors['email_errors'] += 1
                    elif "scheduler" in line.lower():
                        errors['scheduler_errors'] += 1
                    else:
                        errors['other_errors'] += 1
        
        except Exception as e:
            logging.error(f"分析错误日志失败: {e}")
        
        return errors

def setup_logging_from_config(config):
    """从配置设置日志"""
    logger_manager = LoggerManager()
    main_logger = logger_manager.setup_from_config(config)
    
    # 创建专用日志记录器
    scan_logger = ScanLogger(logger_manager.create_scan_logger())
    email_logger = EmailLogger(logger_manager.create_email_logger())
    scheduler_logger = SchedulerLogger(logger_manager.create_scheduler_logger())
    
    return {
        'main': main_logger,
        'scan': scan_logger,
        'email': email_logger,
        'scheduler': scheduler_logger,
        'manager': logger_manager
    }

def test_logging():
    """测试日志功能"""
    # 创建测试配置
    class TestConfig:
        class LoggingConfig:
            log_level = "INFO"
            log_file = "logs/test.log"
            max_log_size_mb = 10
            backup_count = 5
            console_output = True
        
        logging = LoggingConfig()
    
    # 设置日志
    loggers = setup_logging_from_config(TestConfig())
    
    # 测试各种日志
    main_logger = loggers['main']
    scan_logger = loggers['scan']
    email_logger = loggers['email']
    
    main_logger.info("测试主日志记录器")
    
    # 测试扫描日志
    scan_logger.start_scan("192.168.1.1", [80, 443, 22])
    scan_logger.log_port_result("192.168.1.1", 80, True, 0.123)
    scan_logger.log_security_warning("发现高风险端口开放", "CRITICAL")
    scan_logger.end_scan("192.168.1.1", 3, 1, 1)
    
    # 测试邮件日志
    email_logger.log_email_start(["<EMAIL>"], "测试邮件")
    email_logger.log_email_success(["<EMAIL>"])
    
    print("日志测试完成，请检查 logs/ 目录下的日志文件")

if __name__ == "__main__":
    test_logging()


        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>端口扫描安全审计报告</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 900px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .header h1 {
                    color: #007bff;
                    margin: 0;
                    font-size: 28px;
                }
                .info-box {
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 25px;
                    border-left: 4px solid #007bff;
                }
                .stats {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 15px;
                    margin: 25px 0;
                }
                .stat-item {
                    text-align: center;
                    padding: 20px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;
                }
                .stat-number {
                    font-size: 32px;
                    font-weight: bold;
                    color: #007bff;
                    display: block;
                }
                .stat-label {
                    color: #6c757d;
                    font-size: 14px;
                    margin-top: 5px;
                }
                .warning-box {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 25px;
                }
                .critical-box {
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 25px;
                }
                .safe-box {
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 25px;
                }
                .port-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    font-size: 14px;
                }
                .port-table th, .port-table td {
                    border: 1px solid #ddd;
                    padding: 12px 8px;
                    text-align: left;
                }
                .port-table th {
                    background-color: #007bff;
                    color: white;
                    font-weight: bold;
                }
                .port-table tr:nth-child(even) {
                    background-color: #f8f9fa;
                }
                .port-open {
                    color: #dc3545;
                    font-weight: bold;
                }
                .port-closed {
                    color: #6c757d;
                }
                .risk-critical {
                    background-color: #f8d7da;
                    color: #721c24;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                .risk-medium {
                    background-color: #fff3cd;
                    color: #856404;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                .risk-low {
                    background-color: #d1ecf1;
                    color: #0c5460;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                .risk-safe {
                    background-color: #d4edda;
                    color: #155724;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    color: #6c757d;
                    font-size: 14px;
                }
                ul {
                    margin: 10px 0;
                    padding-left: 20px;
                }
                li {
                    margin: 8px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔒 端口扫描安全审计报告</h1>
                    <p>自动化安全扫描系统</p>
                </div>
                
                <div class="info-box">
                    <h3>📋 扫描信息</h3>
                    <p><strong>目标主机:</strong> 139.196.221.237</p>
                    <p><strong>扫描时间:</strong> 2025-09-25 15:11:26</p>
                    <p><strong>扫描端口数:</strong> 16 个</p>
                </div>
                
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-number">12</span>
                        <div class="stat-label">开放端口</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4</span>
                        <div class="stat-label">关闭端口</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">9</span>
                        <div class="stat-label">安全警告</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">6</span>
                        <div class="stat-label">高风险警告</div>
                    </div>
                </div>
        
                <div class="critical-box">
                    <h3>🚨 高风险警告 (6 个)</h3>
                    <ul>
            <li><strong>端口 21 (FTP - 文件传输协议(明文传输)) 开放</strong></li><li><strong>端口 135 (RPC - 远程过程调用) 开放</strong></li><li><strong>端口 445 (SMB/CIFS - 服务器消息块协议) 开放</strong></li><li><strong>端口 5900 (VNC - 远程桌面(弱认证风险)) 开放</strong></li><li><strong>端口 6379 (Redis - Redis数据库(无认证风险)) 开放</strong></li><li><strong>端口 27017 (MongoDB - MongoDB数据库(无认证风险)) 开放</strong></li></ul></div>
                <div class="warning-box">
                    <h3>⚠️ 其他安全警告 (3 个)</h3>
                    <ul>
            <li>端口 53 (DNS - 域名系统(可能被用于DDoS)) 开放</li><li>端口 3306 (MySQL - MySQL数据库) 开放</li><li>端口 8080 (HTTP代理/备用Web服务) 开放</li></ul></div>
                <h3>📊 端口扫描详情</h3>
                <table class="port-table">
                    <thead>
                        <tr>
                            <th>端口</th>
                            <th>状态</th>
                            <th>风险等级</th>
                            <th>服务描述</th>
                        </tr>
                    </thead>
                    <tbody>
        
                        <tr>
                            <td><strong>21</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-critical">高风险</span></td>
                            <td>FTP - 文件传输协议(明文传输)</td>
                        </tr>
            
                        <tr>
                            <td><strong>22</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-safe">安全</span></td>
                            <td>未知服务</td>
                        </tr>
            
                        <tr>
                            <td><strong>23</strong></td>
                            <td class="port-closed">关闭</td>
                            <td><span class="risk-safe">安全</span></td>
                            <td>未知服务</td>
                        </tr>
            
                        <tr>
                            <td><strong>53</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-medium">中风险</span></td>
                            <td>DNS - 域名系统(可能被用于DDoS)</td>
                        </tr>
            
                        <tr>
                            <td><strong>80</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-safe">安全</span></td>
                            <td>未知服务</td>
                        </tr>
            
                        <tr>
                            <td><strong>135</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-critical">高风险</span></td>
                            <td>RPC - 远程过程调用</td>
                        </tr>
            
                        <tr>
                            <td><strong>139</strong></td>
                            <td class="port-closed">关闭</td>
                            <td><span class="risk-safe">安全</span></td>
                            <td>未知服务</td>
                        </tr>
            
                        <tr>
                            <td><strong>443</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-safe">安全</span></td>
                            <td>未知服务</td>
                        </tr>
            
                        <tr>
                            <td><strong>445</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-critical">高风险</span></td>
                            <td>SMB/CIFS - 服务器消息块协议</td>
                        </tr>
            
                        <tr>
                            <td><strong>1433</strong></td>
                            <td class="port-closed">关闭</td>
                            <td><span class="risk-safe">安全</span></td>
                            <td>未知服务</td>
                        </tr>
            
                        <tr>
                            <td><strong>3306</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-medium">中风险</span></td>
                            <td>MySQL - MySQL数据库</td>
                        </tr>
            
                        <tr>
                            <td><strong>3389</strong></td>
                            <td class="port-closed">关闭</td>
                            <td><span class="risk-safe">安全</span></td>
                            <td>未知服务</td>
                        </tr>
            
                        <tr>
                            <td><strong>5900</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-critical">高风险</span></td>
                            <td>VNC - 远程桌面(弱认证风险)</td>
                        </tr>
            
                        <tr>
                            <td><strong>6379</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-critical">高风险</span></td>
                            <td>Redis - Redis数据库(无认证风险)</td>
                        </tr>
            
                        <tr>
                            <td><strong>8080</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-medium">中风险</span></td>
                            <td>HTTP代理/备用Web服务</td>
                        </tr>
            
                        <tr>
                            <td><strong>27017</strong></td>
                            <td class="port-open">开放</td>
                            <td><span class="risk-critical">高风险</span></td>
                            <td>MongoDB - MongoDB数据库(无认证风险)</td>
                        </tr>
            
                    </tbody>
                </table>
                
                <div class="footer">
                    <p>此报告由自动化安全扫描系统生成</p>
                    <p>如有疑问，请联系系统管理员</p>
                    <p>生成时间: 2025-09-25 15:11:26</p>
                </div>
            </div>
        </body>
        </html>
        
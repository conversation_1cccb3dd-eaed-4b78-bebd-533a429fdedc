端口扫描安全审计报告
============================================================
扫描时间: 2025-09-25 14:41:00
目标主机: 139.196.221.237
扫描端口数: 16 个

扫描统计:
------------------------------
开放端口: 12 个
关闭端口: 4 个
安全警告: 9 个
高风险警告: 6 个

安全警告:
------------------------------
  ⚠️  端口 21 (FTP - 文件传输协议(明文传输)) 开放
  ⚠️  端口 135 (RPC - 远程过程调用) 开放
  ⚠️  端口 445 (SMB/CIFS - 服务器消息块协议) 开放
  ⚠️  端口 5900 (VNC - 远程桌面(弱认证风险)) 开放
  ⚠️  端口 6379 (Redis - Redis数据库(无认证风险)) 开放
  ⚠️  端口 27017 (MongoDB - MongoDB数据库(无认证风险)) 开放
  ⚠️  端口 53 (DNS - 域名系统(可能被用于DDoS)) 开放
  ⚠️  端口 3306 (MySQL - MySQL数据库) 开放
  ⚠️  端口 8080 (HTTP代理/备用Web服务) 开放

🚨 高风险端口 (6 个):
  🚨 端口 21 (FTP - 文件传输协议(明文传输)) 开放
  🚨 端口 135 (RPC - 远程过程调用) 开放
  🚨 端口 445 (SMB/CIFS - 服务器消息块协议) 开放
  🚨 端口 5900 (VNC - 远程桌面(弱认证风险)) 开放
  🚨 端口 6379 (Redis - Redis数据库(无认证风险)) 开放
  🚨 端口 27017 (MongoDB - MongoDB数据库(无认证风险)) 开放

端口状态详情:
------------------------------
  端口    21: 开放 [高风险]
  端口    22: 开放
  端口    23: 关闭
  端口    53: 开放 [中风险]
  端口    80: 开放
  端口   135: 开放 [高风险]
  端口   139: 关闭
  端口   443: 开放
  端口   445: 开放 [高风险]
  端口  1433: 关闭
  端口  3306: 开放 [中风险]
  端口  3389: 关闭
  端口  5900: 开放 [高风险]
  端口  6379: 开放 [高风险]
  端口  8080: 开放 [中风险]
  端口 27017: 开放 [高风险]
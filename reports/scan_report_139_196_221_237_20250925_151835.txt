端口扫描安全审计报告
============================================================
扫描时间: 2025-09-25 15:18:35
目标主机: 139.196.221.237
扫描端口数: 24 个

扫描统计:
------------------------------
开放端口: 7 个
关闭端口: 17 个
安全警告: 5 个
高风险警告: 2 个

安全警告:
------------------------------
  ⚠️  端口 3389 (RDP - 远程桌面协议) 开放
  ⚠️  端口 27017 (MongoDB - MongoDB数据库(无认证风险)) 开放
  ⚠️  端口 53 (DNS - 域名系统(可能被用于DDoS)) 开放
  ⚠️  端口 1433 (MSSQL - Microsoft SQL Server) 开放
  ⚠️  端口 3306 (MySQL - MySQL数据库) 开放

🚨 高风险端口 (2 个):
  🚨 端口 3389 (RDP - 远程桌面协议) 开放
  🚨 端口 27017 (MongoDB - MongoDB数据库(无认证风险)) 开放

端口状态详情:
------------------------------
  端口    21: 关闭
  端口    22: 关闭
  端口    23: 关闭
  端口    53: 开放 [中风险]
  端口    80: 开放
  端口   111: 关闭
  端口   135: 关闭
  端口   139: 关闭
  端口   443: 开放
  端口   445: 关闭
  端口   873: 关闭
  端口  1433: 开放 [中风险]
  端口  1434: 关闭
  端口  1521: 关闭
  端口  3306: 开放 [中风险]
  端口  3389: 开放 [高风险]
  端口  5432: 关闭
  端口  5900: 关闭
  端口  6379: 关闭
  端口  8080: 关闭
  端口 11211: 关闭
  端口 27017: 开放 [高风险]
  端口 27018: 关闭
  端口 27019: 关闭
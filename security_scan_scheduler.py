#!/usr/bin/env python3
# security_scan_scheduler.py - 安全扫描定时任务主控制脚本

import sys
import os
import argparse
import time
from datetime import datetime
from typing import List, Dict

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from config_manager import ConfigManager
from logger_config import setup_logging_from_config
from scheduler import SchedulerManager, create_pid_file, remove_pid_file
from report_generator import ReportGenerator
from email_sender import EmailSender
import security_scan

class SecurityScanService:
    """安全扫描服务"""
    
    def __init__(self, config_file: str = "scan_config.json"):
        self.config_manager = ConfigManager(config_file)
        self.config = self.config_manager.load_config()
        
        # 设置日志
        self.loggers = setup_logging_from_config(self.config)
        self.main_logger = self.loggers['main']
        self.scan_logger = self.loggers['scan']
        self.email_logger = self.loggers['email']
        self.scheduler_logger = self.loggers['scheduler']
        
        # 初始化组件
        self.report_generator = ReportGenerator()
        self.scheduler_manager = SchedulerManager(self.config)
        
        # 邮件发送器
        self.email_sender = None
        self._init_email_sender()
        
        self.main_logger.info("安全扫描服务初始化完成")
    
    def _init_email_sender(self):
        """初始化邮件发送器"""
        try:
            smtp_config = {
                'smtp_server': self.config.smtp.smtp_server,
                'smtp_port': self.config.smtp.smtp_port,
                'username': self.config.smtp.username,
                'password': self.config.smtp.password,
                'use_tls': self.config.smtp.use_tls,
                'use_ssl': getattr(self.config.smtp, 'use_ssl', False),
                'sender_name': self.config.smtp.sender_name
            }
            self.email_sender = EmailSender(smtp_config)
            self.main_logger.info("邮件发送器初始化成功")
        except Exception as e:
            self.main_logger.error(f"邮件发送器初始化失败: {e}")
    
    def perform_scan(self, host: str = None) -> Dict:
        """
        执行安全扫描
        
        Args:
            host: 目标主机，如果为None则扫描配置中的所有主机
            
        Returns:
            扫描结果字典
        """
        scan_start_time = datetime.now()
        self.main_logger.info("开始执行安全扫描任务")
        
        # 确定扫描目标
        hosts_to_scan = [host] if host else self.config.scan.target_hosts
        all_results = {}
        
        for target_host in hosts_to_scan:
            try:
                self.scan_logger.start_scan(target_host, self.config.scan.default_ports)
                
                # 执行端口扫描
                results = {}
                for port in self.config.scan.default_ports:
                    is_open = security_scan.check_port(target_host, port, self.config.scan.timeout)
                    results[port] = is_open
                    self.scan_logger.log_port_result(target_host, port, is_open)
                
                # 安全检查
                warnings, critical_warnings = self.report_generator.security_check(results)
                
                # 记录扫描结果
                open_ports = sum(results.values())
                self.scan_logger.end_scan(
                    target_host, 
                    len(results), 
                    open_ports, 
                    len(warnings)
                )
                
                # 记录安全警告
                for warning in critical_warnings:
                    self.scan_logger.log_security_warning(warning, "CRITICAL")
                
                for warning in warnings:
                    if warning not in critical_warnings:
                        self.scan_logger.log_security_warning(warning, "WARNING")
                
                all_results[target_host] = {
                    'results': results,
                    'warnings': warnings,
                    'critical_warnings': critical_warnings,
                    'scan_time': scan_start_time.strftime("%Y-%m-%d %H:%M:%S")
                }
                
                self.main_logger.info(
                    f"主机 {target_host} 扫描完成: "
                    f"开放端口 {open_ports}/{len(results)}, "
                    f"安全警告 {len(warnings)} 个"
                )
                
            except Exception as e:
                self.main_logger.error(f"扫描主机 {target_host} 失败: {e}")
                all_results[target_host] = {
                    'error': str(e),
                    'scan_time': scan_start_time.strftime("%Y-%m-%d %H:%M:%S")
                }
        
        scan_duration = (datetime.now() - scan_start_time).total_seconds()
        self.main_logger.info(f"扫描任务完成，耗时: {scan_duration:.2f}s")
        
        return all_results
    
    def generate_and_send_reports(self, scan_results: Dict) -> bool:
        """
        生成并发送报告
        
        Args:
            scan_results: 扫描结果字典
            
        Returns:
            是否成功发送
        """
        if not self.email_sender:
            self.main_logger.error("邮件发送器未初始化，无法发送报告")
            return False
        
        success_count = 0
        total_hosts = len(scan_results)
        
        for host, result in scan_results.items():
            try:
                if 'error' in result:
                    self.main_logger.warning(f"跳过发送主机 {host} 的报告（扫描失败）")
                    continue
                
                # 生成报告
                report_files = self.report_generator.save_report(
                    host=host,
                    results=result['results'],
                    warnings=result['warnings'],
                    critical_warnings=result['critical_warnings']
                )
                
                # 准备邮件内容
                subject = self.config.email.subject_template.format(
                    host=host,
                    date=datetime.now().strftime("%Y-%m-%d")
                )
                
                html_content = report_files['html_content']
                text_content = f"""
安全扫描报告

扫描时间: {result['scan_time']}
目标主机: {host}
开放端口: {sum(result['results'].values())} 个
安全警告: {len(result['warnings'])} 个
高风险警告: {len(result['critical_warnings'])} 个

详细信息请查看附件。
                """.strip()
                
                # 准备附件
                attachments = []
                if self.config.email.include_attachments:
                    attachments = [report_files['text'], report_files['html']]
                
                # 发送邮件
                self.email_logger.log_email_start(
                    self.config.email.recipients, 
                    subject
                )
                
                success = self.email_sender.send_email(
                    recipients=self.config.email.recipients,
                    subject=subject,
                    html_content=html_content,
                    text_content=text_content,
                    attachments=attachments,
                    max_retries=self.config.email.max_retries
                )
                
                if success:
                    self.email_logger.log_email_success(self.config.email.recipients)
                    success_count += 1
                else:
                    self.email_logger.log_email_failure(
                        self.config.email.recipients, 
                        "发送失败"
                    )
                
            except Exception as e:
                self.main_logger.error(f"生成或发送主机 {host} 的报告失败: {e}")
                self.email_logger.log_email_failure(
                    self.config.email.recipients, 
                    str(e)
                )
        
        self.main_logger.info(f"报告发送完成: {success_count}/{total_hosts} 成功")
        return success_count == total_hosts
    
    def run_scan_task(self):
        """运行扫描任务（供调度器调用）"""
        try:
            self.scheduler_logger.log_job_start("security_scan")
            start_time = time.time()
            
            # 执行扫描
            scan_results = self.perform_scan()
            
            # 发送报告
            if scan_results:
                self.generate_and_send_reports(scan_results)
            
            duration = time.time() - start_time
            self.scheduler_logger.log_job_complete("security_scan", duration)
            
        except Exception as e:
            self.scheduler_logger.log_job_error("security_scan", str(e))
            self.main_logger.error(f"扫描任务执行失败: {e}")
    
    def start_scheduler(self):
        """启动定时调度器"""
        try:
            # 设置扫描任务调度
            self.scheduler_manager.setup_scan_schedule(self.run_scan_task)
            
            # 启动调度器
            self.main_logger.info("启动定时调度器...")
            self.scheduler_manager.start()
            
        except KeyboardInterrupt:
            self.main_logger.info("收到中断信号，停止调度器...")
            self.stop_scheduler()
        except Exception as e:
            self.main_logger.error(f"调度器启动失败: {e}")
    
    def stop_scheduler(self):
        """停止调度器"""
        self.scheduler_manager.stop()
        self.main_logger.info("调度器已停止")
    
    def get_status(self) -> Dict:
        """获取服务状态"""
        return {
            'config_file': self.config_manager.config_file,
            'scheduler': self.scheduler_manager.get_status(),
            'email_configured': self.email_sender is not None,
            'target_hosts': self.config.scan.target_hosts,
            'recipients': self.config.email.recipients
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='安全扫描定时任务系统')
    parser.add_argument('-c', '--config', default='scan_config.json', 
                       help='配置文件路径')
    parser.add_argument('-m', '--mode', choices=['daemon', 'once', 'status'], 
                       default='daemon', help='运行模式')
    parser.add_argument('-H', '--host', help='指定扫描的主机（仅once模式）')
    parser.add_argument('--pid-file', default='scheduler.pid', 
                       help='PID文件路径')
    
    args = parser.parse_args()
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        print("请先运行 python3 config_manager.py 创建配置文件")
        sys.exit(1)
    
    # 创建服务实例
    try:
        service = SecurityScanService(args.config)
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        sys.exit(1)
    
    # 验证配置
    errors = service.config_manager.validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        print(f"\n请编辑配置文件: {args.config}")
        sys.exit(1)
    
    if args.mode == 'once':
        # 单次执行模式
        print("🔍 执行单次扫描...")
        scan_results = service.perform_scan(args.host)
        
        if scan_results:
            print("📧 发送报告...")
            success = service.generate_and_send_reports(scan_results)
            if success:
                print("✅ 扫描和报告发送完成")
            else:
                print("⚠️ 扫描完成，但报告发送失败")
        else:
            print("❌ 扫描失败")
    
    elif args.mode == 'status':
        # 状态查看模式
        status = service.get_status()
        print("📊 系统状态:")
        print(f"  配置文件: {status['config_file']}")
        print(f"  邮件配置: {'✅' if status['email_configured'] else '❌'}")
        print(f"  目标主机: {len(status['target_hosts'])} 个")
        print(f"  收件人: {len(status['recipients'])} 个")
        print(f"  调度器运行: {'✅' if status['scheduler']['running'] else '❌'}")
        print(f"  定时任务: {'✅' if status['scheduler']['config']['enabled'] else '❌'}")
    
    elif args.mode == 'daemon':
        # 守护进程模式
        print("🚀 启动定时扫描服务...")
        
        # 创建PID文件
        if create_pid_file(args.pid_file):
            print(f"📝 PID文件已创建: {args.pid_file}")
        
        try:
            service.start_scheduler()
        finally:
            # 清理PID文件
            remove_pid_file(args.pid_file)
            print("🧹 清理完成")

if __name__ == "__main__":
    main()

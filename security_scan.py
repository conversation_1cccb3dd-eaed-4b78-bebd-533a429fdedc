#!/usr/bin/env python3
# port_scanner.py - 服务器端口安全审计脚本

import socket
import datetime
import sys
import argparse

def check_port(host, port, timeout=3):
    """检查单个端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        return False

def scan_ports(host, ports, timeout=3):
    """扫描多个端口"""
    results = {}
    open_ports = []
    closed_ports = []
    
    print(f"开始扫描 {host} 的端口...")
    print("-" * 50)
    
    for port in ports:
        status = check_port(host, port, timeout)
        if status:
            open_ports.append(port)
            print(f"端口 {port:5d}: 开放 ✓")
        else:
            closed_ports.append(port)
            print(f"端口 {port:5d}: 关闭 ✗")
        results[port] = status
    
    print("-" * 50)
    print(f"开放端口: {len(open_ports)} 个")
    print(f"关闭端口: {len(closed_ports)} 个")
    
    return results

def security_check(results):
    """安全检查 - 识别危险端口"""
    # 完整的危险端口列表
    danger_ports = {
        # 远程访问和管理
        21: "FTP - 文件传输协议(明文传输)",
        23: "Telnet - 远程登录(明文传输)",
        512: "rexec - 远程执行(明文传输)",
        513: "rlogin - 远程登录(明文传输)",
        514: "rsh - 远程shell(明文传输)",
        
        # Windows相关
        135: "RPC - 远程过程调用",
        137: "NetBIOS-NS - NetBIOS名称服务",
        138: "NetBIOS-DGM - NetBIOS数据报服务",
        139: "NetBIOS-SSN - NetBIOS会话服务",
        445: "SMB/CIFS - 服务器消息块协议",
        1433: "MSSQL - Microsoft SQL Server",
        1434: "MSSQL Monitor - SQL Server监控",
        1521: "Oracle - Oracle数据库",
        3389: "RDP - 远程桌面协议",
        
        # 数据库服务
        3306: "MySQL - MySQL数据库",
        5432: "PostgreSQL - PostgreSQL数据库",
        27017: "MongoDB - MongoDB数据库(无认证风险)",
        27018: "MongoDB - MongoDB分片端口",
        27019: "MongoDB - MongoDB配置端口",
        6379: "Redis - Redis数据库(无认证风险)",
        11211: "Memcached - 内存缓存系统",
        9200: "Elasticsearch - 搜索引擎",
        9300: "Elasticsearch - 集群通信",
        
        # 远程管理
        2222: "DirectAdmin - 控制面板",
        2082: "cPanel - 控制面板",
        2083: "cPanel SSL - 控制面板SSL",
        2086: "WebHost Manager - 主机管理",
        2087: "WebHost Manager SSL - 主机管理SSL",
        2095: "Webmail - 网络邮件",
        2096: "Webmail SSL - 网络邮件SSL",
        
        # 其他危险服务
        53: "DNS - 域名系统(可能被用于DDoS)",
        5900: "VNC - 远程桌面(弱认证风险)",
        5901: "VNC - 远程桌面备用端口",
        6000: "X11 - X Window系统(图形界面)",
        6667: "IRC - 互联网中继聊天",
        6697: "IRC SSL - 加密IRC",
        873: "rsync - 文件同步服务",
        9999: "常见后门端口",
        3128: "Squid代理 - HTTP代理",
        8080: "HTTP代理/备用Web服务",
        8000: "HTTP备用端口",
        8888: "HTTP备用端口/代理",
        1080: "SOCKS代理",
        32768: "Solaris portmapper",
        32771: "Solaris portmapper",
        
        # 工业控制系统
        44818: "EtherNet/IP - 工业协议",
        47808: "BACnet - 建筑自动化",
        102: "Siemens S7 - 西门子PLC",
        502: "Modbus - 工业通信协议",
        
        # 其他
        161: "SNMP - 简单网络管理协议",
        162: "SNMP Trap - SNMP陷阱",
        1900: "SSDP - 简单服务发现协议",
        5353: "mDNS - 多播DNS",
        111: "Portmapper - 端口映射",
        1194: "OpenVPN - 虚拟私人网络",
        1723: "PPTP - 点对点隧道协议",
        5060: "SIP - 会话初始协议",
        5061: "SIP TLS - 加密SIP"
    }
    
    warnings = []
    critical_warnings = []
    medium_warnings = []
    low_warnings = []
    
    # 按风险等级分类
    critical_ports = [21, 23, 135, 139, 445, 3389, 5900, 27017, 6379, 11211, 9200]
    medium_ports = [53, 1433, 1521, 3306, 5432, 2082, 2083, 2222, 8080, 8000, 8888]
    
    for port, is_open in results.items():
        if is_open and port in danger_ports:
            warning_msg = f"端口 {port} ({danger_ports[port]}) 开放"
            
            if port in critical_ports:
                critical_warnings.append(warning_msg)
            elif port in medium_ports:
                medium_warnings.append(warning_msg)
            else:
                low_warnings.append(warning_msg)
    
    # 按严重程度排序
    warnings = critical_warnings + medium_warnings + low_warnings
    return warnings, critical_warnings

def save_report(host, results, warnings, critical_warnings, filename=None):
    """保存扫描报告"""
    if not filename:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"port_scan_report_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"端口扫描安全审计报告\n")
        f.write(f"扫描时间: {datetime.datetime.now()}\n")
        f.write(f"目标主机: {host}\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("端口状态详情:\n")
        for port, status in sorted(results.items()):
            status_text = "开放" if status else "关闭"
            f.write(f"  端口 {port:5d}: {status_text}\n")
        
        f.write(f"\n总计: {sum(results.values())} 个端口开放, {len(results) - sum(results.values())} 个端口关闭\n\n")
        
        if warnings:
            f.write("安全警告:\n")
            f.write("-" * 30 + "\n")
            for warning in warnings:
                f.write(f"  ⚠️  {warning}\n")
            
            if critical_warnings:
                f.write(f"\n🚨 高风险端口 ({len(critical_warnings)} 个):\n")
                for warning in critical_warnings:
                    f.write(f"  🚨 {warning}\n")
        else:
            f.write("\n✅ 未发现已知危险端口开放\n")
    
    print(f"报告已保存到: {filename}")
    return filename

def main():
    parser = argparse.ArgumentParser(description='服务器端口安全审计工具')
    parser.add_argument('host', help='目标服务器IP地址')
    parser.add_argument('-p', '--ports', help='要扫描的端口(逗号分隔)', 
                       
default='21,22,23,53,80,111,135,139,443,445,1433,1434,1521,3306,3389,5432,5900,6379,8080,873,11211,27017,27018,27019')
    parser.add_argument('-t', '--timeout', type=int, default=3, help='连接超时时间(秒)')
    parser.add_argument('-o', '--output', help='输出报告文件名')
    
    args = parser.parse_args()
    
    # 解析端口列表
    try:
        ports = [int(p.strip()) for p in args.ports.split(',')]
    except ValueError:
        print("错误: 端口格式不正确")
        sys.exit(1)
    
    # 执行扫描
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] 服务器端口安全审计")
    print(f"目标主机: {args.host}")
    print(f"扫描端口数: {len(ports)}")
    print()
    
    results = scan_ports(args.host, ports, args.timeout)
    warnings, critical_warnings = security_check(results)
    
    # 显示安全警告
    if warnings:
        print("\n⚠️  安全警告:")
        for warning in warnings:
            print(f"  {warning}")
        
        if critical_warnings:
            print(f"\n🚨 高风险警告 ({len(critical_warnings)} 个):")
            for warning in critical_warnings:
                print(f"  🚨 {warning}")
    else:
        print("\n✅ 未发现已知危险端口开放")
    
    # 保存报告
    save_report(args.host, results, warnings, critical_warnings, args.output)

if __name__ == "__main__":
    # 如果没有参数，使用默认配置
    if len(sys.argv) == 1:
        # 修改这里为您要扫描的服务器IP
        HOST = "***************"  # 请修改为目标服务器IP
        PORTS = [21, 22, 23, 53, 80, 111, 135, 139, 443, 445, 1433, 1434, 1521, 
                3306, 3389, 5432, 5900, 6379, 8080, 873, 11211, 27017, 27018, 27019]
        
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] 服务器端口安全审计")
        print(f"目标主机: {HOST}")
        print(f"扫描端口数: {len(PORTS)}")
        print()
        
        results = scan_ports(HOST, PORTS)
        warnings, critical_warnings = security_check(results)
        
        if warnings:
            print("\n⚠️  安全警告:")
            for warning in warnings:
                print(f"  {warning}")
            
            if critical_warnings:
                print(f"\n🚨 高风险警告 ({len(critical_warnings)} 个):")
                for warning in critical_warnings:
                    print(f"  🚨 {warning}")
        else:
            print("\n✅ 未发现已知危险端口开放")
        
        # 保存报告
        save_report(HOST, results, warnings, critical_warnings)
    else:
        main()

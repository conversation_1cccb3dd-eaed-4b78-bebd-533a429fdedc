#!/usr/bin/env python3
# scheduler.py - 定时任务调度器

import schedule
import time
import threading
import logging
import signal
import sys
from datetime import datetime, timedelta
from typing import Callable, Optional
import os

class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.scheduler_thread = None
        self.jobs = []
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在停止调度器...")
        self.stop()
        sys.exit(0)
    
    def add_interval_job(self, func: Callable, interval_hours: int, 
                        job_name: str = None, run_immediately: bool = False):
        """
        添加间隔执行任务
        
        Args:
            func: 要执行的函数
            interval_hours: 间隔小时数
            job_name: 任务名称
            run_immediately: 是否立即执行一次
        """
        if not job_name:
            job_name = func.__name__
        
        job = schedule.every(interval_hours).hours.do(func)
        job.tag = job_name
        self.jobs.append(job)
        
        self.logger.info(f"已添加间隔任务: {job_name}, 间隔: {interval_hours} 小时")
        
        if run_immediately:
            self.logger.info(f"立即执行任务: {job_name}")
            try:
                func()
            except Exception as e:
                self.logger.error(f"立即执行任务失败: {e}")
    
    def add_daily_job(self, func: Callable, time_str: str, 
                     job_name: str = None, run_immediately: bool = False):
        """
        添加每日定时任务
        
        Args:
            func: 要执行的函数
            time_str: 时间字符串，格式如 "02:00"
            job_name: 任务名称
            run_immediately: 是否立即执行一次
        """
        if not job_name:
            job_name = func.__name__
        
        job = schedule.every().day.at(time_str).do(func)
        job.tag = job_name
        self.jobs.append(job)
        
        self.logger.info(f"已添加每日任务: {job_name}, 时间: {time_str}")
        
        if run_immediately:
            self.logger.info(f"立即执行任务: {job_name}")
            try:
                func()
            except Exception as e:
                self.logger.error(f"立即执行任务失败: {e}")
    
    def add_weekly_job(self, func: Callable, day: str, time_str: str,
                      job_name: str = None, run_immediately: bool = False):
        """
        添加每周定时任务
        
        Args:
            func: 要执行的函数
            day: 星期几，如 "monday", "tuesday" 等
            time_str: 时间字符串，格式如 "02:00"
            job_name: 任务名称
            run_immediately: 是否立即执行一次
        """
        if not job_name:
            job_name = func.__name__
        
        job = getattr(schedule.every(), day.lower()).at(time_str).do(func)
        job.tag = job_name
        self.jobs.append(job)
        
        self.logger.info(f"已添加每周任务: {job_name}, 时间: {day} {time_str}")
        
        if run_immediately:
            self.logger.info(f"立即执行任务: {job_name}")
            try:
                func()
            except Exception as e:
                self.logger.error(f"立即执行任务失败: {e}")
    
    def add_cron_job(self, func: Callable, cron_expression: str,
                    job_name: str = None, run_immediately: bool = False):
        """
        添加cron风格的定时任务（简化版）
        
        Args:
            func: 要执行的函数
            cron_expression: cron表达式，格式: "分 时 日 月 周"
            job_name: 任务名称
            run_immediately: 是否立即执行一次
        """
        if not job_name:
            job_name = func.__name__
        
        # 解析cron表达式（简化版，只支持基本格式）
        parts = cron_expression.split()
        if len(parts) != 5:
            raise ValueError("cron表达式格式错误，应为: 分 时 日 月 周")
        
        minute, hour, day, month, weekday = parts
        
        # 简化处理，只支持每日定时
        if minute == "0" and hour.isdigit() and day == "*" and month == "*" and weekday == "*":
            time_str = f"{hour.zfill(2)}:00"
            self.add_daily_job(func, time_str, job_name, run_immediately)
        else:
            raise ValueError("当前只支持每日定时的cron表达式，格式: 0 小时 * * *")
    
    def remove_job(self, job_name: str) -> bool:
        """
        移除指定任务
        
        Args:
            job_name: 任务名称
            
        Returns:
            是否成功移除
        """
        removed_jobs = []
        for job in self.jobs:
            if hasattr(job, 'tag') and job.tag == job_name:
                schedule.cancel_job(job)
                removed_jobs.append(job)
        
        for job in removed_jobs:
            self.jobs.remove(job)
        
        if removed_jobs:
            self.logger.info(f"已移除任务: {job_name}")
            return True
        else:
            self.logger.warning(f"未找到任务: {job_name}")
            return False
    
    def list_jobs(self) -> list:
        """列出所有任务"""
        job_list = []
        for job in schedule.jobs:
            job_info = {
                'name': getattr(job, 'tag', 'unknown'),
                'next_run': job.next_run,
                'interval': str(job.interval),
                'unit': job.unit
            }
            job_list.append(job_info)
        return job_list
    
    def _run_scheduler(self):
        """运行调度器（在单独线程中）"""
        self.logger.info("调度器开始运行...")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"调度器运行错误: {e}")
                time.sleep(5)  # 出错后等待5秒再继续
        
        self.logger.info("调度器已停止")
    
    def start(self, daemon: bool = True):
        """
        启动调度器
        
        Args:
            daemon: 是否作为守护线程运行
        """
        if self.running:
            self.logger.warning("调度器已在运行中")
            return
        
        if not self.jobs:
            self.logger.warning("没有任务需要调度")
            return
        
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=daemon)
        self.scheduler_thread.start()
        
        self.logger.info(f"调度器已启动，共 {len(self.jobs)} 个任务")
        
        # 显示下次执行时间
        for job in schedule.jobs:
            job_name = getattr(job, 'tag', 'unknown')
            next_run = job.next_run.strftime("%Y-%m-%d %H:%M:%S") if job.next_run else "未知"
            self.logger.info(f"任务 '{job_name}' 下次执行时间: {next_run}")
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            self.logger.warning("调度器未在运行")
            return
        
        self.running = False
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        self.logger.info("调度器已停止")
    
    def is_running(self) -> bool:
        """检查调度器是否在运行"""
        return self.running
    
    def wait(self):
        """等待调度器结束（阻塞模式）"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            try:
                self.scheduler_thread.join()
            except KeyboardInterrupt:
                self.logger.info("收到中断信号，停止调度器...")
                self.stop()

class SchedulerManager:
    """调度器管理器"""
    
    def __init__(self, config):
        self.config = config
        self.scheduler = TaskScheduler()
        self.logger = logging.getLogger(__name__)
    
    def setup_scan_schedule(self, scan_function: Callable):
        """
        根据配置设置扫描任务调度
        
        Args:
            scan_function: 扫描函数
        """
        if not self.config.schedule.enabled:
            self.logger.info("定时任务已禁用")
            return
        
        schedule_type = self.config.schedule.schedule_type
        
        if schedule_type == "interval":
            self.scheduler.add_interval_job(
                func=scan_function,
                interval_hours=self.config.schedule.interval_hours,
                job_name="security_scan",
                run_immediately=True
            )
        
        elif schedule_type == "daily":
            # 从cron表达式中提取时间
            cron_parts = self.config.schedule.cron_expression.split()
            if len(cron_parts) >= 2:
                hour = cron_parts[1]
                minute = cron_parts[0]
                time_str = f"{hour.zfill(2)}:{minute.zfill(2)}"
            else:
                time_str = "02:00"  # 默认凌晨2点
            
            self.scheduler.add_daily_job(
                func=scan_function,
                time_str=time_str,
                job_name="security_scan",
                run_immediately=True
            )
        
        elif schedule_type == "weekly":
            # 默认每周一执行
            cron_parts = self.config.schedule.cron_expression.split()
            if len(cron_parts) >= 2:
                hour = cron_parts[1]
                minute = cron_parts[0]
                time_str = f"{hour.zfill(2)}:{minute.zfill(2)}"
            else:
                time_str = "02:00"
            
            self.scheduler.add_weekly_job(
                func=scan_function,
                day="monday",
                time_str=time_str,
                job_name="security_scan",
                run_immediately=True
            )
        
        elif schedule_type == "cron":
            self.scheduler.add_cron_job(
                func=scan_function,
                cron_expression=self.config.schedule.cron_expression,
                job_name="security_scan",
                run_immediately=True
            )
        
        else:
            self.logger.error(f"不支持的调度类型: {schedule_type}")
            return
        
        self.logger.info(f"已设置 {schedule_type} 类型的定时扫描任务")
    
    def start(self):
        """启动调度器"""
        self.scheduler.start(daemon=False)
    
    def stop(self):
        """停止调度器"""
        self.scheduler.stop()
    
    def wait(self):
        """等待调度器结束"""
        self.scheduler.wait()
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        return {
            'running': self.scheduler.is_running(),
            'jobs': self.scheduler.list_jobs(),
            'config': {
                'enabled': self.config.schedule.enabled,
                'type': self.config.schedule.schedule_type,
                'interval_hours': self.config.schedule.interval_hours,
                'cron_expression': self.config.schedule.cron_expression
            }
        }

def create_pid_file(pid_file: str = "scheduler.pid"):
    """创建PID文件"""
    try:
        with open(pid_file, 'w') as f:
            f.write(str(os.getpid()))
        return True
    except Exception as e:
        logging.error(f"创建PID文件失败: {e}")
        return False

def remove_pid_file(pid_file: str = "scheduler.pid"):
    """删除PID文件"""
    try:
        if os.path.exists(pid_file):
            os.remove(pid_file)
        return True
    except Exception as e:
        logging.error(f"删除PID文件失败: {e}")
        return False

if __name__ == "__main__":
    # 测试调度器
    logging.basicConfig(level=logging.INFO)
    
    def test_task():
        print(f"测试任务执行: {datetime.now()}")
    
    scheduler = TaskScheduler()
    scheduler.add_interval_job(test_task, 1, "test_job", run_immediately=True)
    
    print("调度器测试开始，按 Ctrl+C 停止...")
    scheduler.start()
    scheduler.wait()

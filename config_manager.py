#!/usr/bin/env python3
# config_manager.py - 配置管理模块

import json
import os
import logging
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class SMTPConfig:
    """SMTP配置"""
    smtp_server: str
    smtp_port: int
    username: str
    password: str
    use_tls: bool = True
    sender_name: str = "安全扫描系统"

@dataclass
class ScanConfig:
    """扫描配置"""
    target_hosts: List[str]
    default_ports: List[int]
    timeout: int = 3
    concurrent_scans: bool = False
    max_threads: int = 10

@dataclass
class ScheduleConfig:
    """定时任务配置"""
    enabled: bool = True
    schedule_type: str = "interval"  # interval, cron, daily, weekly
    interval_hours: int = 24
    cron_expression: str = "0 2 * * *"  # 每天凌晨2点
    timezone: str = "Asia/Shanghai"

@dataclass
class EmailConfig:
    """邮件配置"""
    recipients: List[str]
    subject_template: str = "🔒 安全扫描报告 - {host} - {date}"
    include_attachments: bool = True
    max_retries: int = 3
    send_on_no_issues: bool = True
    send_summary_only: bool = False

@dataclass
class LoggingConfig:
    """日志配置"""
    log_level: str = "INFO"
    log_file: str = "security_scan.log"
    max_log_size_mb: int = 10
    backup_count: int = 5
    console_output: bool = True

@dataclass
class AppConfig:
    """应用程序完整配置"""
    smtp: SMTPConfig
    scan: ScanConfig
    schedule: ScheduleConfig
    email: EmailConfig
    logging: LoggingConfig
    version: str = "1.0.0"
    last_updated: str = ""

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "scan_config.json"):
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self._config = None
    
    def load_config(self) -> AppConfig:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.logger.info(f"配置文件 {self.config_file} 不存在，创建默认配置")
            self._create_default_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 验证和转换配置
            self._config = self._parse_config(config_data)
            self.logger.info("配置文件加载成功")
            return self._config
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.logger.info("使用默认配置")
            return self._create_default_config()
    
    def save_config(self, config: AppConfig = None) -> bool:
        """保存配置文件"""
        try:
            if config:
                self._config = config
            
            if not self._config:
                self.logger.error("没有配置可保存")
                return False
            
            # 更新最后修改时间
            self._config.last_updated = datetime.now().isoformat()
            
            # 转换为字典并保存
            config_dict = asdict(self._config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置已保存到 {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _create_default_config(self) -> AppConfig:
        """创建默认配置"""
        default_config = AppConfig(
            smtp=SMTPConfig(
                smtp_server="smtp.gmail.com",
                smtp_port=587,
                username="<EMAIL>",
                password="your_app_password",
                use_tls=True,
                sender_name="安全扫描系统"
            ),
            scan=ScanConfig(
                target_hosts=["***************"],
                default_ports=[21, 22, 23, 53, 80, 111, 135, 139, 443, 445, 
                              1433, 1434, 1521, 3306, 3389, 5432, 5900, 6379, 
                              8080, 873, 11211, 27017, 27018, 27019],
                timeout=3,
                concurrent_scans=False,
                max_threads=10
            ),
            schedule=ScheduleConfig(
                enabled=True,
                schedule_type="daily",
                interval_hours=24,
                cron_expression="0 2 * * *",
                timezone="Asia/Shanghai"
            ),
            email=EmailConfig(
                recipients=[
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ],
                subject_template="🔒 安全扫描报告 - {host} - {date}",
                include_attachments=True,
                max_retries=3,
                send_on_no_issues=True,
                send_summary_only=False
            ),
            logging=LoggingConfig(
                log_level="INFO",
                log_file="security_scan.log",
                max_log_size_mb=10,
                backup_count=5,
                console_output=True
            ),
            version="1.0.0",
            last_updated=datetime.now().isoformat()
        )
        
        # 保存默认配置
        self._config = default_config
        self.save_config()
        
        return default_config
    
    def _parse_config(self, config_data: Dict) -> AppConfig:
        """解析配置数据"""
        try:
            return AppConfig(
                smtp=SMTPConfig(**config_data.get('smtp', {})),
                scan=ScanConfig(**config_data.get('scan', {})),
                schedule=ScheduleConfig(**config_data.get('schedule', {})),
                email=EmailConfig(**config_data.get('email', {})),
                logging=LoggingConfig(**config_data.get('logging', {})),
                version=config_data.get('version', '1.0.0'),
                last_updated=config_data.get('last_updated', '')
            )
        except Exception as e:
            self.logger.error(f"解析配置失败: {e}")
            raise
    
    def validate_config(self, config: AppConfig = None) -> List[str]:
        """验证配置有效性"""
        if not config:
            config = self._config
        
        if not config:
            return ["配置未加载"]
        
        errors = []
        
        # 验证SMTP配置
        if not config.smtp.smtp_server:
            errors.append("SMTP服务器地址不能为空")
        if not config.smtp.username:
            errors.append("SMTP用户名不能为空")
        if not config.smtp.password:
            errors.append("SMTP密码不能为空")
        if config.smtp.smtp_port <= 0 or config.smtp.smtp_port > 65535:
            errors.append("SMTP端口必须在1-65535之间")
        
        # 验证扫描配置
        if not config.scan.target_hosts:
            errors.append("目标主机列表不能为空")
        if not config.scan.default_ports:
            errors.append("默认端口列表不能为空")
        if config.scan.timeout <= 0:
            errors.append("超时时间必须大于0")
        
        # 验证邮件配置
        if not config.email.recipients:
            errors.append("收件人列表不能为空")
        
        # 验证定时任务配置
        if config.schedule.enabled:
            if config.schedule.schedule_type == "interval" and config.schedule.interval_hours <= 0:
                errors.append("定时间隔必须大于0小时")
        
        return errors
    
    def get_config(self) -> AppConfig:
        """获取当前配置"""
        if not self._config:
            return self.load_config()
        return self._config
    
    def update_smtp_config(self, **kwargs) -> bool:
        """更新SMTP配置"""
        try:
            if not self._config:
                self.load_config()
            
            for key, value in kwargs.items():
                if hasattr(self._config.smtp, key):
                    setattr(self._config.smtp, key, value)
            
            return self.save_config()
        except Exception as e:
            self.logger.error(f"更新SMTP配置失败: {e}")
            return False
    
    def add_recipient(self, email: str) -> bool:
        """添加收件人"""
        try:
            if not self._config:
                self.load_config()
            
            if email not in self._config.email.recipients:
                self._config.email.recipients.append(email)
                return self.save_config()
            
            return True
        except Exception as e:
            self.logger.error(f"添加收件人失败: {e}")
            return False
    
    def remove_recipient(self, email: str) -> bool:
        """移除收件人"""
        try:
            if not self._config:
                self.load_config()
            
            if email in self._config.email.recipients:
                self._config.email.recipients.remove(email)
                return self.save_config()
            
            return True
        except Exception as e:
            self.logger.error(f"移除收件人失败: {e}")
            return False
    
    def add_target_host(self, host: str) -> bool:
        """添加目标主机"""
        try:
            if not self._config:
                self.load_config()
            
            if host not in self._config.scan.target_hosts:
                self._config.scan.target_hosts.append(host)
                return self.save_config()
            
            return True
        except Exception as e:
            self.logger.error(f"添加目标主机失败: {e}")
            return False

def create_sample_config():
    """创建示例配置文件"""
    config_manager = ConfigManager("scan_config.json")
    config = config_manager.load_config()
    
    print("已创建示例配置文件: scan_config.json")
    print("\n请根据您的实际情况修改以下配置项：")
    print("1. SMTP邮箱设置 (smtp 部分)")
    print("2. 收件人列表 (email.recipients)")
    print("3. 目标主机列表 (scan.target_hosts)")
    print("4. 定时任务设置 (schedule 部分)")
    
    return config

if __name__ == "__main__":
    # 创建示例配置
    create_sample_config()
